package sentry

import (
	"fmt"
	"log"
	"time"

	"stellar-go/internal/config"

	"github.com/getsentry/sentry-go"
	sentrygin "github.com/getsentry/sentry-go/gin"
	"github.com/gin-gonic/gin"
)

// Initialize initializes Sentry with the provided configuration
func Initialize(cfg *config.Config) error {
	// Skip initialization if no DSN is provided
	if cfg.Sentry.DSN == "" {
		log.Println("Sentry DSN not provided, skipping Sentry initialization")
		return nil
	}

	// Determine environment for Sentry
	sentryEnv := cfg.Sentry.Environment
	if sentryEnv == "" {
		sentryEnv = cfg.Environment
	}

	// Initialize Sentry
	err := sentry.Init(sentry.ClientOptions{
		Dsn:              cfg.Sentry.DSN,
		Environment:      sentryEnv,
		Release:          cfg.Sentry.Release,
		SampleRate:       cfg.Sentry.SampleRate,
		TracesSampleRate: cfg.Sentry.TracesSampleRate,
		Debug:            cfg.Sentry.Debug,
		AttachStacktrace: true,
		BeforeSend: func(event *sentry.Event, hint *sentry.EventHint) *sentry.Event {
			// Filter out test errors in development
			if cfg.Environment == "development" && event.Message == "This is a test error for monitoring" {
				return nil
			}
			return event
		},
	})

	if err != nil {
		return fmt.Errorf("failed to initialize Sentry: %w", err)
	}

	log.Printf("Sentry initialized successfully for environment: %s", sentryEnv)
	return nil
}

// Middleware returns a Gin middleware that integrates with Sentry
func Middleware() gin.HandlerFunc {
	return sentrygin.New(sentrygin.Options{
		Repanic:         true,
		WaitForDelivery: false,
		Timeout:         2 * time.Second,
	})
}

// CaptureError captures an error and sends it to Sentry
func CaptureError(err error) {
	if err != nil {
		sentry.CaptureException(err)
	}
}

// CaptureMessage captures a message and sends it to Sentry
func CaptureMessage(message string, level sentry.Level) {
	sentry.CaptureMessage(message)
}

// SetUser sets user context for Sentry
func SetUser(userID uint, email string) {
	sentry.ConfigureScope(func(scope *sentry.Scope) {
		scope.SetUser(sentry.User{
			ID:    fmt.Sprintf("%d", userID),
			Email: email,
		})
	})
}

// SetTag sets a tag for Sentry
func SetTag(key, value string) {
	sentry.ConfigureScope(func(scope *sentry.Scope) {
		scope.SetTag(key, value)
	})
}

// SetContext sets additional context for Sentry
func SetContext(key string, context map[string]interface{}) {
	sentry.ConfigureScope(func(scope *sentry.Scope) {
		scope.SetContext(key, context)
	})
}

// Flush waits for all events to be sent to Sentry
func Flush(timeout time.Duration) bool {
	return sentry.Flush(timeout)
}
