# Stellar Go API Configuration Example
# Copy this file to config.yaml and update with your values

# Server configuration
port: 8080
environment: development

# Database configuration
db_host: localhost
db_port: 5432
db_name: stellar
db_user: stellar_user
db_password: your-database-password
db_sslmode: disable

# JWT configuration
secret_key: your-super-secret-jwt-key-change-in-production
backend_url: http://localhost:8080
frontend_url: http://localhost:3000

# Okta configuration
okta_domain: your-domain.okta.com
okta_client_id: your-okta-client-id
okta_client_secret: your-okta-client-secret
okta_redirect_uri: http://localhost:8080/login/callback
idp_admin_group: StellarAdmins

# SCIM configuration
scim_bearer_token: your-scim-bearer-token-for-okta

# Slack configuration (optional)
slack_token: xoxb-your-slack-bot-token
slack_notification_channel_id: C1234567890

# CORS configuration
cors_allowed_origins: "http://localhost:3000,http://localhost:3001"
cors_allow_credentials: true
cors_allowed_methods: "GET,POST,PUT,DELETE,OPTIONS"
cors_allowed_headers: "Content-Type,Content-Length,Accept-Encoding,X-CSRF-Token,Authorization,accept,origin,Cache-Control,X-Requested-With"

# Sentry configuration (optional - for error reporting)
sentry_dsn: "https://<EMAIL>/project-id"
sentry_environment: "development"
sentry_release: "stellar-go@1.0.0"
sentry_sample_rate: 1.0
sentry_traces_sample_rate: 0.1
sentry_debug: false
